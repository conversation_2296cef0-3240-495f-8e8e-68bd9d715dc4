import { tokenCache } from "@/cache";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import { ClerkLoa<PERSON>, Clerk<PERSON>rovider } from "@clerk/clerk-expo";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router/stack";
import { SafeAreaProvider } from "react-native-safe-area-context";

import { NotificationProvider } from "@/features/notification/NotificationContext";
import "@/global.css";
import "@/lib/i18n/config";
import * as Sentry from "@sentry/react-native";
import * as Notifications from "expo-notifications";
import * as SplashScreen from "expo-splash-screen";
import { useCallback, useEffect } from "react";
import { View } from "react-native";
import Loading from "@/components/Loading";

SplashScreen.preventAutoHideAsync();

SplashScreen.setOptions({
  duration: 1000,
  fade: true,
});

Sentry.init({
  dsn: "https://<EMAIL>/4509212323610624",

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    "Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env"
  );
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      enabled: false,
    },
  },
});

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    priority: Notifications.AndroidNotificationPriority.HIGH,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

function RootLayout() {
  return (
    <NotificationProvider>
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        <ClerkLoaded>
          <QueryClientProvider client={queryClient}>
            <SafeAreaProvider>
              <StackScreens />
            </SafeAreaProvider>
          </QueryClientProvider>
        </ClerkLoaded>
      </ClerkProvider>
    </NotificationProvider>
  );
}

function StackScreens() {
  const { isSignedIn, isLoadingUser } = useAppAuth();

  const onLayoutRootView = useCallback(async () => {
    if (isLoadingUser) return null;
    SplashScreen.hide();
  }, [isLoadingUser]);

  if (isLoadingUser) {
    return <Loading />;
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Protected guard={isSignedIn}>
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="events/[id]" />
          <Stack.Screen name="profile-edit" />
        </Stack.Protected>
        <Stack.Protected guard={!isSignedIn}>
          <Stack.Screen name="sign-in" />
          <Stack.Screen name="sign-up" />
        </Stack.Protected>
        <Stack.Screen name="settings" />
      </Stack>
      <View onLayout={onLayoutRootView} />
    </>
  );
}

export default Sentry.wrap(RootLayout);
