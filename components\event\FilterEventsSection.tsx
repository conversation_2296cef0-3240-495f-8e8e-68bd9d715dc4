import { MyEventType } from "@/features/events/model";
import globalStyles from "@/lib/globalStyles";
import React, { useMemo } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import Button from "../Button";
import useEventFilterStore from "@/features/events/useEventFilterStore";
import { MyEventCategory } from "@/features/categories/model";
import MyDatePicker from "../MyDatePicker";
import InputSelect from "../InputSelect";
import { useTranslation } from "react-i18next";

type Props = {
  events: MyEventType[];
  isLoading: boolean;
  onConclude: () => void;
};

const FilterModalAction = ({
  onClean,
  onConclude,
  totalResults,
  isLoading,
}: {
  onClean: () => void;
  onConclude: () => void;
  totalResults: number;
  isLoading: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <View
      style={{
        gap: globalStyles.gap["2xs"],
        width: "100%",
        alignItems: "flex-end",
        marginTop: globalStyles.size.sm,
      }}
    >
      <Button
        // size='sm'
        text={t("search.see_all_n_results", { totalResults })}
        onPress={onConclude}
        style={{ width: "100%" }}
        disabled={totalResults === 0}
        isLoading={isLoading}
      />
      <TouchableOpacity onPress={() => onClean()}>
        <Text
          style={{
            color: globalStyles.colors.primary1,
            textDecorationLine: "underline",
          }}
        >
          {t("search.remove_filters")}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const FilterEventsSection = ({ events, isLoading, onConclude }: Props) => {
  const {
    clearFilter,
    selectedStartDate,
    selectedOrganizer,
    selectedLocation,
    selectedCategory,
    setSelectedOrganizer,
    setSelectedCategory,
    setStartDate,
    setFilter,
  } = useEventFilterStore();

  const { t } = useTranslation();

  // create a function that calculates the nearest and farthest startAt of a events list
  const getMinMaxDate = (events: MyEventType[]) => {
    const sortedEvents = events.sort((a, b) => {
      const aDate = new Date(a.startAt);
      const bDate = new Date(b.startAt);
      return aDate.getTime() - bDate.getTime();
    });

    const minDate = sortedEvents[0]?.startAt;
    const maxDate = sortedEvents[sortedEvents.length - 1]?.startAt;

    return {
      minDate: minDate ? new Date(minDate) : undefined,
      maxDate: maxDate ? new Date(maxDate) : undefined,
    };
  };

  const getEventsCategories = (events: MyEventType[]) => {
    const mergedCategories = events.reduce((acc, event) => {
      const eventCategories = event.categories || [];
      const newCategories = eventCategories.filter(
        (cat) => !acc.find((c) => c._id === cat._id)
      );
      return [...acc, ...newCategories];
    }, [] as MyEventCategory[]);

    return mergedCategories.reduce((acc, cat) => {
      if (!acc.some((c) => c._id === cat._id)) {
        return [...acc, cat];
      }
      return acc;
    }, [] as MyEventCategory[]);
  };

  const getEventsOrganizers = (events: MyEventType[]) =>
    events.reduce((acc, event) => {
      if (!acc.some((org) => org === event.organizer)) {
        return [...acc, event.organizer];
      }
      return acc;
    }, [] as string[]);

  const getEventsLocations = (events: MyEventType[]) =>
    events.reduce((acc, event) => {
      if (!acc.some((loc) => loc === event.location)) {
        return [...acc, event.location];
      }
      return acc;
    }, [] as string[]);

  const categories = useMemo(() => getEventsCategories(events), []);
  const organizers = useMemo(() => getEventsOrganizers(events), []);
  const locations = useMemo(() => getEventsLocations(events), []);
  const minMaxDate = useMemo(() => getMinMaxDate(events), [events]);

  const totalResults = events?.length || 0;

  const organizersItems = useMemo(
    () =>
      getEventsOrganizers(events)
        .map((v) => ({ name: v, value: v }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, organizers]
  );

  const categoriesItems = useMemo(
    () =>
      getEventsCategories(events)
        .map((c) => ({ name: c.name, value: c._id }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, categories]
  );

  const locationsItems = useMemo(
    () =>
      getEventsLocations(events)
        .map((item) => ({ name: item, value: item }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, locations]
  );

  return (
    <View
      style={{
        gap: 25,
      }}
    >
      <View
        style={{
          gap: globalStyles.gap.xs,
          flexDirection: "row",
          width: "100%",
        }}
      >
        <InputSelect
          label={t("common.categories")}
          items={categoriesItems}
          selected={
            selectedCategory
              ? { value: selectedCategory._id, name: selectedCategory.name }
              : undefined
          }
          onSelectItem={(item) => {
            setSelectedCategory({ _id: item.value, name: item.name });
          }}
          style={{
            flex: 1,
          }}
          theme="primary"
          hasActiveColor
          // withSearch
        />

        <InputSelect
          label={t("common.organizers")}
          items={organizersItems}
          selected={
            selectedOrganizer
              ? { value: selectedOrganizer, name: selectedOrganizer }
              : undefined
          }
          onSelectItem={(v) => setSelectedOrganizer(v.value)}
          style={{
            flex: 1,
          }}
          theme="primary"
          hasActiveColor
          alignment="right"
        />
      </View>
      <View
        style={{
          gap: globalStyles.gap.xs,
          flexDirection: "row",
        }}
      >
        <InputSelect
          label={t("common.locations")}
          items={locationsItems}
          selected={
            selectedLocation
              ? { value: selectedLocation, name: selectedLocation }
              : undefined
          }
          onSelectItem={(v) => setFilter("selectedLocation", v.value)}
          style={{
            flex: 1,
          }}
          theme="primary"
          hasActiveColor
          // withSearch
        />
        <MyDatePicker
          label={t("common.by_date")}
          date={selectedStartDate}
          onChange={(timestamp) => setStartDate(new Date(timestamp))}
          style={{
            flex: 1.3,
          }}
          theme="primary"
          hasActiveColor
          minimumDate={minMaxDate.minDate}
          maximumDate={minMaxDate.maxDate}
        />
      </View>
      <FilterModalAction
        onClean={clearFilter}
        onConclude={onConclude}
        totalResults={totalResults}
        isLoading={isLoading}
      />
    </View>
  );
};

export default FilterEventsSection;
