import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { Entypo, Ionicons } from "@expo/vector-icons";
import { MyEventType } from "@/features/events/model";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import { RenderIf } from "@/components/RenderIf";
import globalStyles from "@/lib/globalStyles";
import Button from "@/components/Button";
import useHasUserFavoritedEvent from "@/features/events/useHasUserFavoritedEvent";
import useToggleFavorite from "@/features/events/useToggleFavorite";
import ImageView from "react-native-image-viewing";
import { useDebounce } from "@/hooks/useDebounce";
import { useTranslation } from "react-i18next";
import { buttonStyles } from "@/components/Button";

const EventDetailImage = ({
  event,
  onShare,
  onShowActivities,
  hasActivities,
}: {
  event: MyEventType;
  onShare: () => void;
  onShowActivities: () => void;
  hasActivities: boolean;
}) => {
  const imgUrl = event.medias?.[0]?.url;

  const uris =
    event.medias?.flatMap((media) =>
      media?.url ? [{ uri: media.url }] : []
    ) || [];

  const [showImageView, setShowImageView] = useState(false);
  const [currentImageIndex, setImageIndex] = useState(0);

  const { user } = useAppAuth();
  const { t } = useTranslation();

  const {
    isFavorite,
    isLoading: isFavoriteLoading,
    refetch: refetchHasUserFavoritedEvent,
    setIsFavorite,
  } = useHasUserFavoritedEvent({
    eventId: event._id,
    userId: user?.id,
  });

  const onToggleFavoriteSuccess = useDebounce(
    refetchHasUserFavoritedEvent,
    5000
  );

  const { toggleFavorite, isLoading } = useToggleFavorite({
    onSuccess: () => {
      setIsFavorite(!isFavorite);
      onToggleFavoriteSuccess();
    },
  });

  return (
    <View style={styles.container}>
      <TouchableOpacity
        activeOpacity={0.5}
        onPress={() => setShowImageView(true)}
      >
        <Image
          // resizeMode="contain"
          style={styles.image}
          source={
            imgUrl
              ? { uri: imgUrl }
              : require("@/assets/images/event-placeholder.jpg")
          }
        />
      </TouchableOpacity>
      {imgUrl && (
        <ImageView
          images={uris}
          visible={showImageView}
          imageIndex={currentImageIndex}
          onRequestClose={() => setShowImageView(false)}
          onImageIndexChange={(index) => setImageIndex(index)}
          FooterComponent={({ imageIndex }) => (
            <View className="p-2xs flex flex-row justify-center">
              <Text className="text-white">
                {imageIndex + 1} / {uris.length}
              </Text>
            </View>
          )}
        />
      )}
      <RenderIf isTrue={!!user}>
        <TouchableOpacity
          disabled={isLoading || isFavoriteLoading}
          style={styles.favorite}
          onPress={() =>
            toggleFavorite({
              eventId: event._id,
              userId: user!.id,
            })
          }
        >
          <RenderIf isTrue={isLoading || isFavoriteLoading}>
            <ActivityIndicator
              size="small"
              color={globalStyles.colors.primary1}
            />
          </RenderIf>
          <RenderIf isTrue={!isLoading && !isFavoriteLoading}>
            <Ionicons
              name={isFavorite ? "heart-sharp" : "heart-outline"}
              size={20}
              color={globalStyles.colors.primary1}
            />
          </RenderIf>
        </TouchableOpacity>
      </RenderIf>
      <View style={styles.buttonsContainer}>
        <Button
          activeOpacity={0.8}
          size="sm"
          onPress={onShare}
          type="outline"
          style={[styles.actionButton]}
        >
          <View style={styles.buttonContent}>
            <Text style={buttonStyles.outlineText}>{t("common.share")}</Text>
            <Entypo
              name="share"
              size={14}
              color={globalStyles.rgba().primary1}
            />
          </View>
        </Button>

        {hasActivities && (
          <Button
            activeOpacity={0.8}
            size="sm"
            onPress={onShowActivities}
            style={[styles.actionButton]}
          >
            <View style={styles.buttonContent}>
              <Text style={buttonStyles.primaryText}>
                {t("event.view_activities")}
              </Text>
              <Entypo name="list" size={14} color={globalStyles.rgba().white} />
            </View>
          </Button>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    alignItems: "center",
    gap: globalStyles.gap["xs"],
  },
  image: {
    width: "100%",
    height: undefined,
    aspectRatio: 4 / 3,
    borderRadius: globalStyles.rounded.sm,
    overflow: "hidden",
    borderColor: globalStyles.colors.light.primary,
    borderWidth: 2,
    backgroundColor: globalStyles.rgba().light.primary,
  },
  favorite: {
    position: "absolute",
    top: globalStyles.gap.xs,
    right: globalStyles.gap.xs,
    width: 30,
    height: 30,
    borderRadius: globalStyles.rounded.full,
    backgroundColor: globalStyles.colors.light.primary,
    justifyContent: "center",
    alignItems: "center",
  },
  buttonsContainer: {
    flexDirection: "row",
    gap: globalStyles.gap["xs"],
    justifyContent: "center",
    alignItems: "center",
  },
  actionButton: {
    paddingHorizontal: globalStyles.gap["xs"],
  },
  buttonContent: {
    flexDirection: "row",
    gap: globalStyles.gap["2xs"],
    alignItems: "center",
    justifyContent: "center",
  },
});
export default EventDetailImage;
